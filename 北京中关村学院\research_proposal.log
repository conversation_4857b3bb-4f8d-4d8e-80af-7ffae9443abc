This is XeTeX, Version 3.141592653-2.6-0.999996 (TeX Live 2024) (preloaded format=xelatex 2025.2.26)  25 JUN 2025 15:42
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**research_proposal
(./research_proposal.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(d:/texlive/2024/texmf-dist/tex/latex/ctex/ctexart.cls (d:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctexbackend.cfg
File: ctexbackend.cfg 2022/07/14 v2.5.10 Backend configuration file (CTEX)
) (d:/texlive/2024/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 
 (d:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-02-20 L3 backend support: XeTeX
\g__graphics_track_int=\count184
\l__pdf_internal_box=\box51
\g__pdf_backend_object_int=\count185
\g__pdf_backend_annotation_int=\count186
\g__pdf_backend_link_int=\count187
))
Document Class: ctexart 2022/07/14 v2.5.10 Chinese adapter for class article (CTEX)
(d:/texlive/2024/texmf-dist/tex/latex/ctex/ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
) (d:/texlive/2024/texmf-dist/tex/latex/ctex/ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
) (d:/texlive/2024/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX
 (d:/texlive/2024/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\l__ctex_tmp_int=\count188
\l__ctex_tmp_box=\box52
\l__ctex_tmp_dim=\dimen140
\g__ctex_section_depth_int=\count189
\g__ctex_font_size_int=\count190
 (d:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctexopts.cfg
File: ctexopts.cfg 2022/07/14 v2.5.10 Option configuration file (CTEX)
) (d:/texlive/2024/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(d:/texlive/2024/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count191
\c@section=\count192
\c@subsection=\count193
\c@subsubsection=\count194
\c@paragraph=\count195
\c@subparagraph=\count196
\c@figure=\count197
\c@table=\count198
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen141
) (d:/texlive/2024/texmf-dist/tex/latex/ctex/engine/ctex-engine-xetex.def
File: ctex-engine-xetex.def 2022/07/14 v2.5.10 XeLaTeX adapter (CTEX)
 (d:/texlive/2024/texmf-dist/tex/xelatex/xecjk/xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX
 (d:/texlive/2024/texmf-dist/tex/latex/l3packages/xtemplate/xtemplate.sty (d:/texlive/2024/texmf-dist/tex/latex/l3packages/xtemplate/xtemplate-2023-10-10.sty
Package: xtemplate 2023-10-10 L3 Experimental prototype document functions
\l__xtemplate_tmp_dim=\dimen142
\l__xtemplate_tmp_int=\count199
\l__xtemplate_tmp_muskip=\muskip16
\l__xtemplate_tmp_skip=\skip50
))
\l__xeCJK_tmp_int=\count266
\l__xeCJK_tmp_box=\box53
\l__xeCJK_tmp_dim=\dimen143
\l__xeCJK_tmp_skip=\skip51
\g__xeCJK_space_factor_int=\count267
\l__xeCJK_begin_int=\count268
\l__xeCJK_end_int=\count269
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip52
\c__xeCJK_none_node=\count270
\g__xeCJK_node_int=\count271
\c__xeCJK_CJK_node_dim=\dimen144
\c__xeCJK_CJK-space_node_dim=\dimen145
\c__xeCJK_default_node_dim=\dimen146
\c__xeCJK_CJK-widow_node_dim=\dimen147
\c__xeCJK_normalspace_node_dim=\dimen148
\c__xeCJK_default-space_node_skip=\skip53
\l__xeCJK_ccglue_skip=\skip54
\l__xeCJK_ecglue_skip=\skip55
\l__xeCJK_punct_kern_skip=\skip56
\l__xeCJK_indent_box=\box54
\l__xeCJK_last_penalty_int=\count272
\l__xeCJK_last_bound_dim=\dimen149
\l__xeCJK_last_kern_dim=\dimen150
\l__xeCJK_widow_penalty_int=\count273

Package xtemplate Info: Declaring object type 'xeCJK/punctuation' taking 0
(xtemplate)             argument(s) on line 2396.

\l__xeCJK_fixed_punct_width_dim=\dimen151
\l__xeCJK_mixed_punct_width_dim=\dimen152
\l__xeCJK_middle_punct_width_dim=\dimen153
\l__xeCJK_fixed_margin_width_dim=\dimen154
\l__xeCJK_mixed_margin_width_dim=\dimen155
\l__xeCJK_middle_margin_width_dim=\dimen156
\l__xeCJK_bound_punct_width_dim=\dimen157
\l__xeCJK_bound_margin_width_dim=\dimen158
\l__xeCJK_margin_minimum_dim=\dimen159
\l__xeCJK_kerning_total_width_dim=\dimen160
\l__xeCJK_same_align_margin_dim=\dimen161
\l__xeCJK_different_align_margin_dim=\dimen162
\l__xeCJK_kerning_margin_width_dim=\dimen163
\l__xeCJK_kerning_margin_minimum_dim=\dimen164
\l__xeCJK_bound_dim=\dimen165
\l__xeCJK_reverse_bound_dim=\dimen166
\l__xeCJK_margin_dim=\dimen167
\l__xeCJK_minimum_bound_dim=\dimen168
\l__xeCJK_kerning_margin_dim=\dimen169
\g__xeCJK_family_int=\count274
\l__xeCJK_fam_int=\count275
\g__xeCJK_fam_allocation_int=\count276
\l__xeCJK_verb_case_int=\count277
\l__xeCJK_verb_exspace_skip=\skip57
 (d:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.sty (d:/texlive/2024/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-02-18 L3 Experimental document command parser
)
Package: fontspec 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTeX
 (d:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count278
\l__fontspec_language_int=\count279
\l__fontspec_strnum_int=\count280
\l__fontspec_tmp_int=\count281
\l__fontspec_tmpa_int=\count282
\l__fontspec_tmpb_int=\count283
\l__fontspec_tmpc_int=\count284
\l__fontspec_em_int=\count285
\l__fontspec_emdef_int=\count286
\l__fontspec_strong_int=\count287
\l__fontspec_strongdef_int=\count288
\l__fontspec_tmpa_dim=\dimen170
\l__fontspec_tmpb_dim=\dimen171
\l__fontspec_tmpc_dim=\dimen172
 (d:/texlive/2024/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (d:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.cfg))) (d:/texlive/2024/texmf-dist/tex/xelatex/xecjk/xeCJK.cfg
File: xeCJK.cfg 2022/08/05 v3.9.1 Configuration file for xeCJK package
))
\ccwd=\dimen173
\l__ctex_ccglue_skip=\skip58
)
\l__ctex_ziju_dim=\dimen174
 (d:/texlive/2024/texmf-dist/tex/latex/zhnumber/zhnumber.sty
Package: zhnumber 2022/07/14 v3.0 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count289
\l__zhnum_tmp_int=\count290
 (d:/texlive/2024/texmf-dist/tex/latex/zhnumber/zhnumber-utf8.cfg
File: zhnumber-utf8.cfg 2022/07/14 v3.0 Chinese numerals with UTF8 encoding
))
\l__ctex_heading_skip=\skip59
 (d:/texlive/2024/texmf-dist/tex/latex/ctex/scheme/ctex-scheme-chinese-article.def
File: ctex-scheme-chinese-article.def 2022/07/14 v2.5.10 Chinese scheme for article (CTEX)
 (d:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctex-name-utf8.cfg
File: ctex-name-utf8.cfg 2022/07/14 v2.5.10 Caption with encoding UTF-8 (CTEX)
)) (d:/texlive/2024/texmf-dist/tex/latex/ctex/fontset/ctex-fontset-windows.def
File: ctex-fontset-windows.def 2022/07/14 v2.5.10 Windows fonts definition (CTEX)

Package fontspec Info: Could not resolve font "KaiTi/B" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimHei/I" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Could not resolve font "SimSun/BI" (it probably doesn't
(fontspec)             exist).


Package fontspec Info: Font family 'SimSun(0)' created for font 'SimSun' with
(fontspec)             options
(fontspec)             [Script={CJK},BoldFont={SimHei},ItalicFont={KaiTi}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"SimSun/OT:script=hani;language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"SimHei/OT:script=hani;language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->"KaiTi/OT:script=hani;language=dflt;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.: 

)) (d:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctex.cfg
File: ctex.cfg 2022/07/14 v2.5.10 Configuration file (CTEX)
) (d:/texlive/2024/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (d:/texlive/2024/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (d:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count291
\Gm@cntv=\count292
\c@Gm@tempcnt=\count293
\Gm@bindingoffset=\dimen175
\Gm@wd@mp=\dimen176
\Gm@odd@mp=\dimen177
\Gm@even@mp=\dimen178
\Gm@layoutwidth=\dimen179
\Gm@layoutheight=\dimen180
\Gm@layouthoffset=\dimen181
\Gm@layoutvoffset=\dimen182
\Gm@dimlist=\toks18
) (d:/texlive/2024/texmf-dist/tex/latex/tools/indentfirst.sty
Package: indentfirst 2023/07/02 v1.03 Indent first paragraph (DPC)
) (d:/texlive/2024/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\labelindent=\skip60
\enit@outerparindent=\dimen183
\enit@toks=\toks19
\enit@inbox=\box55
\enit@count@id=\count294
\enitdp@description=\count295
) (d:/texlive/2024/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-01-20 v7.01h Hypertext links for LaTeX
 (d:/texlive/2024/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (d:/texlive/2024/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (d:/texlive/2024/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (d:/texlive/2024/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (d:/texlive/2024/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (d:/texlive/2024/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (d:/texlive/2024/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (d:/texlive/2024/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count296
) (d:/texlive/2024/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count297
)
\@linkdim=\dimen184
\Hy@linkcounter=\count298
\Hy@pagecounter=\count299
 (d:/texlive/2024/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-01-20 v7.01h Hyperref: PDFDocEncoding definition (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count300
 (d:/texlive/2024/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-01-20 v7.01h Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `unicode' set `true' on input line 4062.
Package hyperref Info: Hyper figures OFF on input line 4179.
Package hyperref Info: Link nesting OFF on input line 4184.
Package hyperref Info: Hyper index ON on input line 4187.
Package hyperref Info: Plain pages OFF on input line 4194.
Package hyperref Info: Backreferencing OFF on input line 4199.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4446.
\c@Hy@tempcnt=\count301
 (d:/texlive/2024/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4784.
\XeTeXLinkMargin=\dimen185
 (d:/texlive/2024/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (d:/texlive/2024/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count302
\Field@Width=\dimen186
\Fld@charsize=\dimen187
Package hyperref Info: Hyper figures OFF on input line 6063.
Package hyperref Info: Link nesting OFF on input line 6068.
Package hyperref Info: Hyper index ON on input line 6071.
Package hyperref Info: backreferencing OFF on input line 6078.
Package hyperref Info: Link coloring OFF on input line 6083.
Package hyperref Info: Link coloring with OCG OFF on input line 6088.
Package hyperref Info: PDF/A mode OFF on input line 6093.
 (d:/texlive/2024/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count303
\c@Item=\count304
\c@Hfootnote=\count305
)
Package hyperref Info: Driver (autodetected): hxetex.
 (d:/texlive/2024/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2024-01-20 v7.01h Hyperref driver for XeTeX
 (d:/texlive/2024/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\pdfm@box=\box56
\c@Hy@AnnotLevel=\count306
\HyField@AnnotCount=\count307
\Fld@listcount=\count308
\c@bookmark@seq@number=\count309
 (d:/texlive/2024/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (d:/texlive/2024/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
) (d:/texlive/2024/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip61
) (d:/texlive/2024/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
) (d:/texlive/2024/texmf-dist/tex/latex/tools/tabularx.sty
Package: tabularx 2023/07/08 v2.11c `tabularx' package (DPC)
 (d:/texlive/2024/texmf-dist/tex/latex/tools/array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen188
\ar@mcellbox=\box57
\extrarowheight=\dimen189
\NC@list=\toks20
\extratabsurround=\skip62
\backup@length=\skip63
\ar@cellbox=\box58
)
\TX@col@width=\dimen190
\TX@old@table=\dimen191
\TX@old@col=\dimen192
\TX@target=\dimen193
\TX@delta=\dimen194
\TX@cols=\count310
\TX@ftn=\toks21
) (d:/texlive/2024/texmf-dist/tex/generic/ulem/ulem.sty
\UL@box=\box59
\UL@hyphenbox=\box60
\UL@skip=\skip64
\UL@hook=\toks22
\UL@height=\dimen195
\UL@pe=\count311
\UL@pixel=\dimen196
\ULC@box=\box61
Package: ulem 2019/11/18
\ULdepth=\dimen197
) (./research_proposal.aux)
\openout1 = `research_proposal.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 20.
LaTeX Font Info:    ... okay on input line 20.

Package fontspec Info: Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup4
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 20.
LaTeX Font Info:    Redeclaring math accent \acute on input line 20.
LaTeX Font Info:    Redeclaring math accent \grave on input line 20.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 20.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 20.
LaTeX Font Info:    Redeclaring math accent \bar on input line 20.
LaTeX Font Info:    Redeclaring math accent \breve on input line 20.
LaTeX Font Info:    Redeclaring math accent \check on input line 20.
LaTeX Font Info:    Redeclaring math accent \hat on input line 20.
LaTeX Font Info:    Redeclaring math accent \dot on input line 20.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 20.
LaTeX Font Info:    Redeclaring math symbol \colon on input line 20.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 20.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 20.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 20.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 20.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 20.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 20.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 20.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 20.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 20.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 20.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 20.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 20.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 20.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 20.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 20.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 20.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/lmr/m/n on input line 20.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 20.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 20.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/b/n on input line 20.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 20.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 20.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 20.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/lmr/b/it on input line 20.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 20.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 20.

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 455.24411pt, 71.13188pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=702.78308pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=35.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package hyperref Info: Link coloring OFF on input line 20.
(./research_proposal.out) (./research_proposal.out)
\@outlinefile=\write3
\openout3 = `research_proposal.out'.


Overfull \hbox (24.0pt too wide) in paragraph at lines 32--33
[][] 
 []


Overfull \hbox (24.0pt too wide) in paragraph at lines 39--40
[][] 
 []

LaTeX Font Info:    Font shape `TU/SimSun(0)/m/sl' in size <12> not available
(Font)              Font shape `TU/SimSun(0)/m/it' tried instead on input line 60.
[1

] [2] [3] (./research_proposal.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2022/07/14>
 ***********
Package rerunfilecheck Info: File `research_proposal.out' has not changed.
(rerunfilecheck)             Checksum: 0511E241567212A8E3730228D88D87FE;463.
 ) 
Here is how much of TeX's memory you used:
 15269 strings out of 474773
 345339 string characters out of 5761289
 1956842 words of memory out of 5000000
 37240 multiletter control sequences out of 15000+600000
 563113 words of font info for 74 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 93i,9n,97p,352b,336s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on research_proposal.pdf (3 pages).
