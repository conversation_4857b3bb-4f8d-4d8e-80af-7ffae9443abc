<!doctype html>
<html lang="zh-CN">
 <head> 
  <meta charset="utf-8">  
  <meta http-equiv="content-type" content="text/html; charset=utf-8"> 
  <meta name="renderer" content="webkit"> 
  <meta name="force-rendering" content="webkit"> 
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"> 
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"> 
  <meta name="report" content="{&quot;pid&quot;: &quot;blog&quot;, &quot;spm&quot;:&quot;1001.2101&quot;}">  
  <meta http-equiv="Cache-Control" content="no-siteapp">
  <link rel="alternate" media="handheld" href="#"> 
  <meta name="shenma-site-verification" content="5a59773ab8077d4a62bf469ab966a63b_1497598848"> 
  <meta name="applicable-device" content="pc">  
  <title>stm32教程之三重ADC交错采样</title>  
  <meta name="keywords" content="单片机ad交错采样"> 
  <meta name="csdn-baidu-search" content="{&quot;autorun&quot;:true,&quot;install&quot;:true,&quot;keyword&quot;:&quot;单片机ad交错采样&quot;}"> 
  <meta name="description" content="文章浏览阅读8k次，点赞32次，收藏119次。本文详细介绍了如何在STM32F407ZGT6单片机上实现三重ADC交错采样，以提高采样率至7.2Msps。通过配置TIM8定时器触发ADC，利用HAL库进行设置，实现了更灵活的采样率调整。作者分享了配置步骤和代码实现，包括ADC时钟频率、触发源、DMA模式等关键设置，并警告在高工作负载时可能存在的不稳定性问题。
"> 
  <link rel="stylesheet" type="text/css" href="https://csdnimg.cn/release/blogv2/dist/pc/css/detail_enter-5b2b1521a0.min.css">  
  <link rel="stylesheet" type="text/css" href="https://csdnimg.cn/release/blogv2/dist/pc/themesSkin/skin-1024/skin-1024-ecd36efea2.min.css">    
  <meta name="toolbar" content="{&quot;type&quot;:&quot;0&quot;,&quot;fixModel&quot;:&quot;1&quot;}">    
  <link rel="stylesheet" type="text/css" href="https://csdnimg.cn/public/sandalstrap/1.4/css/sandalstrap.min.css"> 
  <style>.MathJax_Display {text-align: center; margin: 1em 0em; position: relative; display: block!important; text-indent: 0; max-width: none; max-height: none; min-width: 0; min-height: 0; width: 100%} .MathJax .merror {background-color: #FFFF88; color: #CC0000; border: 1px solid #CC0000; padding: 1px 3px; font-style: normal; font-size: 90%} .MathJax .MJX-monospace {font-family: monospace} .MathJax .MJX-sans-serif {font-family: sans-serif} #MathJax_Tooltip {background-color: InfoBackground; color: InfoText; border: 1px solid black; box-shadow: 2px 2px 5px #AAAAAA; -webkit-box-shadow: 2px 2px 5px #AAAAAA; -moz-box-shadow: 2px 2px 5px #AAAAAA; -khtml-box-shadow: 2px 2px 5px #AAAAAA; filter: progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true'); padding: 3px 4px; z-index: 401; position: absolute; left: 0; top: 0; width: auto; height: auto; display: none} .MathJax {display: inline; font-style: normal; font-weight: normal; line-height: normal; font-size: 100%; font-size-adjust: none; text-indent: 0; text-align: left; text-transform: none; letter-spacing: normal; word-spacing: normal; word-wrap: normal; white-space: nowrap; float: none; direction: ltr; max-width: none; max-height: none; min-width: 0; min-height: 0; border: 0; padding: 0; margin: 0} .MathJax:focus, body :focus .MathJax {display: inline-table} .MathJax.MathJax_FullWidth {text-align: center; display: table-cell!important; width: 10000em!important} .MathJax img, .MathJax nobr, .MathJax a {border: 0; padding: 0; margin: 0; max-width: none; max-height: none; min-width: 0; min-height: 0; vertical-align: 0; line-height: normal; text-decoration: none} img.MathJax_strut {border: 0!important; padding: 0!important; margin: 0!important; vertical-align: 0!important} .MathJax span {display: inline; position: static; border: 0; padding: 0; margin: 0; vertical-align: 0; line-height: normal; text-decoration: none} .MathJax nobr {white-space: nowrap!important} .MathJax img {display: inline!important; float: none!important} .MathJax * {transition: none; -webkit-transition: none; -moz-transition: none; -ms-transition: none; -o-transition: none} .MathJax_Processing {visibility: hidden; position: fixed; width: 0; height: 0; overflow: hidden} .MathJax_Processed {display: none!important} .MathJax_ExBox {display: block!important; overflow: hidden; width: 1px; height: 60ex; min-height: 0; max-height: none} .MathJax .MathJax_EmBox {display: block!important; overflow: hidden; width: 1px; height: 60em; min-height: 0; max-height: none} .MathJax_LineBox {display: table!important} .MathJax_LineBox span {display: table-cell!important; width: 10000em!important; min-width: 0; max-width: none; padding: 0; border: 0; margin: 0} .MathJax .MathJax_HitBox {cursor: text; background: white; opacity: 0; filter: alpha(opacity=0)} .MathJax .MathJax_HitBox * {filter: none; opacity: 1; background: transparent} #MathJax_Tooltip * {filter: none; opacity: 1; background: transparent} @font-face {font-family: MathJax_Main; src: url('https://csdnimg.cn/release/blog_mathjax/fonts/HTML-CSS/TeX/woff/MathJax_Main-Regular.woff?V=2.7.2') format('woff'), url('https://csdnimg.cn/release/blog_mathjax/fonts/HTML-CSS/TeX/otf/MathJax_Main-Regular.otf?V=2.7.2') format('opentype')} @font-face {font-family: MathJax_Main-bold; src: url('https://csdnimg.cn/release/blog_mathjax/fonts/HTML-CSS/TeX/woff/MathJax_Main-Bold.woff?V=2.7.2') format('woff'), url('https://csdnimg.cn/release/blog_mathjax/fonts/HTML-CSS/TeX/otf/MathJax_Main-Bold.otf?V=2.7.2') format('opentype')} @font-face {font-family: MathJax_Main-italic; src: url('https://csdnimg.cn/release/blog_mathjax/fonts/HTML-CSS/TeX/woff/MathJax_Main-Italic.woff?V=2.7.2') format('woff'), url('https://csdnimg.cn/release/blog_mathjax/fonts/HTML-CSS/TeX/otf/MathJax_Main-Italic.otf?V=2.7.2') format('opentype')} @font-face {font-family: MathJax_Math-italic; src: url('https://csdnimg.cn/release/blog_mathjax/fonts/HTML-CSS/TeX/woff/MathJax_Math-Italic.woff?V=2.7.2') format('woff'), url('https://csdnimg.cn/release/blog_mathjax/fonts/HTML-CSS/TeX/otf/MathJax_Math-Italic.otf?V=2.7.2') format('opentype')} @font-face {font-family: MathJax_Caligraphic; src: url('https://csdnimg.cn/release/blog_mathjax/fonts/HTML-CSS/TeX/woff/MathJax_Caligraphic-Regular.woff?V=2.7.2') format('woff'), url('https://csdnimg.cn/release/blog_mathjax/fonts/HTML-CSS/TeX/otf/MathJax_Caligraphic-Regular.otf?V=2.7.2') format('opentype')} @font-face {font-family: MathJax_Size1; src: url('https://csdnimg.cn/release/blog_mathjax/fonts/HTML-CSS/TeX/woff/MathJax_Size1-Regular.woff?V=2.7.2') format('woff'), url('https://csdnimg.cn/release/blog_mathjax/fonts/HTML-CSS/TeX/otf/MathJax_Size1-Regular.otf?V=2.7.2') format('opentype')} @font-face {font-family: MathJax_Size2; src: url('https://csdnimg.cn/release/blog_mathjax/fonts/HTML-CSS/TeX/woff/MathJax_Size2-Regular.woff?V=2.7.2') format('woff'), url('https://csdnimg.cn/release/blog_mathjax/fonts/HTML-CSS/TeX/otf/MathJax_Size2-Regular.otf?V=2.7.2') format('opentype')} @font-face {font-family: MathJax_Size3; src: url('https://csdnimg.cn/release/blog_mathjax/fonts/HTML-CSS/TeX/woff/MathJax_Size3-Regular.woff?V=2.7.2') format('woff'), url('https://csdnimg.cn/release/blog_mathjax/fonts/HTML-CSS/TeX/otf/MathJax_Size3-Regular.otf?V=2.7.2') format('opentype')} @font-face {font-family: MathJax_Size4; src: url('https://csdnimg.cn/release/blog_mathjax/fonts/HTML-CSS/TeX/woff/MathJax_Size4-Regular.woff?V=2.7.2') format('woff'), url('https://csdnimg.cn/release/blog_mathjax/fonts/HTML-CSS/TeX/otf/MathJax_Size4-Regular.otf?V=2.7.2') format('opentype')} .MathJax .noError {vertical-align: ; font-size: 90%; text-align: left; color: black; padding: 1px 3px; border: 1px solid}</style>      
 </head> 
 <body class="nodata  " style=""> 
  <div id="toolbarBox" style="min-height: 48px;"></div>    
  <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/pc/css/blog_code-01256533b5.min.css"> 
  <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/chart-3456820cac.css"> 
  <link rel="stylesheet" href="https://g.csdnimg.cn/lib/swiper/6.0.4/css/swiper.css">   
  <div class="main_father clearfix  justify-content-center mainfather-concision" style="height:100%;"> 
   <div class="container clearfix container-concision" id="mainBox" style="width:100%">  
    <main style="width:100%">  
     <div class="blog-content-box"> 
      <div class="article-header-box"> 
       <div class="article-header"> 
        <div class="article-title-box"> 
         <h1 class="title-article" id="articleContentId">stm32教程之三重ADC交错采样</h1> 
        </div>  
       </div> 
      </div> 
      <div id="blogHuaweiyunAdvert" class=""></div>  
      <article class="baidu_pl"> 
       <div id="article_content" class="article_content clearfix"> 
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/kdoc_html_views-1a98987dfd.css"> 
        <link rel="stylesheet" href="https://csdnimg.cn/release/blogv2/dist/mdeditor/css/editerView/ck_htmledit_views-8d6d398833.css"> 
        <div id="content_views" class="htmledit_views atom-one-dark"> 
         <p>ps：本文基于stm32F407ZGT6单片机</p> 
         <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;stm32F4单片机单通道采集的最大采样率为2.4M，所以有时会难以满足较高频率的采样，于是查阅芯片手册，发现stm32F4支持多重ADC采集，利用每个通道的转换时间，错位采样，从而提高采样率，最大把采样率开到2.4*3=7.2M.&nbsp; （去年初学ADC时研究的，基于cube和HAL库的三重ADC交错采样参考资料不多，我也是研究了一段时间，写下这篇博客希望可以帮助到更多人。）</p> 
         <p>示意图如下：</p> 
         <p><img alt="" height="450" src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fi-blog.csdnimg.cn%2Fblog_migrate%2Fd81432c7a2d673191a517228688e522d.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461" width="911"></p> 
         <p>以下是官方例子的说明，</p> 
         <p>In this example, the system clock is 144MHz, APB2 = 72MHz and ADC clock = APB2 /2.&nbsp;<br> Since ADCCLK= 36MHz and Conversion rate = 5 cycles&nbsp;<br> ==&gt; Conversion Time = 36M/5cyc = 7.2Msps</p> 
         <p>这是利用软件触发ADC连续采样的，我试了一下，是可行的。网上为数不多的参考资料也是按上述连续转换模式下的。</p> 
         <p>但是我是比较习惯使用定时器触发采样的，因为采样率连续可调，比连续转换灵活一些。</p> 
         <p>所以我开始尝试定时器触发三重ADC！</p> 
         <p>配置如下：打开TIM8，挂在APB2上，为144M</p> 
         <p><img alt="" height="423" src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fi-blog.csdnimg.cn%2Fblog_migrate%2F371c7b4fb1ac934b62031de8f86cae0d.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461" width="723"></p> 
         <p>时钟频率配置多少呢？</p> 
         <p>打开芯片手册：</p> 
         <p><img alt="" height="179" src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fi-blog.csdnimg.cn%2Fblog_migrate%2Fa2f56b4cdc77fc445a759289f11fcb47.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461" width="904"></p> 
         <p>&nbsp;由此可知 触发源只需要触发ADC1 ADC2和3是根据芯片设定好的三重模式固有时间后自动开启采集的</p> 
         <p>故ADC1只需配置2.4M&nbsp; 144M/60=2.4M</p> 
         <p><img alt="" height="674" src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fi-blog.csdnimg.cn%2Fblog_migrate%2Fdc1e232aef46e49c1489ba057f2b6985.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461" width="1050"></p> 
         <p>&nbsp;ADC如何配置呢？</p> 
         <p>再看芯片手册！</p> 
         <p><img alt="" height="291" src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fi-blog.csdnimg.cn%2Fblog_migrate%2F0df9dc1f06b967cb424de96ddcd9fb94.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461" width="863"></p> 
         <p>&nbsp;</p> 
         <p>&nbsp;DMA mode2&nbsp; ！</p> 
         <p>ADC1配置如下：</p> 
         <p>（选通道12是因为它纯净 干扰少）</p> 
         <p>记得打开DMA !</p> 
         <p>配置类似单通道ADC＋定时器触发！</p> 
         <p><img alt="" height="768" src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fi-blog.csdnimg.cn%2Fblog_migrate%2F726f6575bd153a02584efc4b72f4802d.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461" width="708"></p> 
         <p>ADC2/3 同上 这里就不赘述了！</p> 
         <p>完成！</p> 
         <p>生成代码！</p> 
         <p>&nbsp;</p> 
         <p>代码里如何写呢？</p> 
         <p>再再再次打开芯片手册！！</p> 
         <p><img alt="" height="567" src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fi-blog.csdnimg.cn%2Fblog_migrate%2F877ce4975b637c894f9f6d1b78ccc894.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461" width="878"></p> 
         <p>原来如此！我感觉我可以！</p> 
         <p>开启ADC123和TIM8:(注意三重ADC交替模式下，ADC1有固有开启函数)</p> 
         <p>&nbsp;<img alt="" height="120" src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fi-blog.csdnimg.cn%2Fblog_migrate%2Fd52f6e10a8d60e6b0d331598955be454.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461" width="630"></p> 
         <p><img alt="" height="287" src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fi-blog.csdnimg.cn%2Fblog_migrate%2Fbb651399d07642d68793dd23f722e605.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461" width="816"></p> 
         <p>&nbsp;编译！烧录！</p> 
         <p style="text-align:center;"><img alt="" src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fi-blog.csdnimg.cn%2Fblog_migrate%2F1f0cc97b6846a7121799e6f72ddc5ffd.gif&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461"></p> 
         <p>去年测试的，疫情在家就不给测试图了QAQ</p> 
         <p>去年测试时是用信号发生器给了100khz正弦波信号，一个周期采到了72个点左右。</p> 
         <p>ps：据学长说这种方式不稳定，单片机工作量大时可能会出bug。</p> 
         <p>&nbsp;</p> 
         <p>&nbsp;</p> 
         <p>&nbsp;</p> 
         <p></p> 
         <p></p> 
         <p></p> 
         <p></p> 
         <p></p> 
         <p></p> 
        </div> 
       </div> 
      </article>   
      <img src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fimg-home.csdnimg.cn%2Fimages%2F20211209110851.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461" alt="pdf_watermark" width="0" height="0" style="display: none"> 
      <div class="print_watermark"></div> 
      <div class="print_watermark_info"> 
       <p>内容来源：csdn.net</p> 
       <p>作者昵称：浅许</p> 
       <p>原文链接：https://blog.csdn.net/weixin_52178036/article/details/122900461</p> 
       <p>作者主页：https://blog.csdn.net/weixin_52178036</p> 
      </div> 
      <style>
    .print_watermark, .print_watermark_info {
      display: none
    }
    @media print {
      body {
        -webkit-print-color-adjust: exact; /* Chrome, Safari */
        color-adjust: exact; /* Firefox */
        background-image: none !important;
      }
      * {
        -webkit-print-color-adjust: exact;
      }
      .blog-content-box {
        padding: 0;
      }
      .blog-content-box .article-header .article-info-box > div:not(.article-bar-top){
        display: none !important;
      }
      .blog-content-box .article-header .article-info-box  .article-bar-top img{
          display:none
      }
      .blog-content-box .article-header .article-info-box > .article-bar-top .bar-content > *:not(.follow-nickName):not(.time){
          display: none !important;
      }
      .print_watermark {
        display: block;
        position: fixed;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        z-index: 999;
        background-image: url('https://img-home.csdnimg.cn/images/20211209110851.png');
        background-size: 180px auto;
        background-repeat: repeat;
      }
      .print_watermark_info {
        display: block;
        position: fixed;
        bottom: 16px;
        right: 0;
        z-index: 1000;
        color: #e8e8ed;
        font-size: 12px;
        ocapity: .5
      }
      @page {
        margin: 0 10mm 10mm;
        size: landscape;
      }
      body, article {
        width: 100%;
        margin: 0;
        padding: 0;
      }
      #csdn-toolbar,.main_father > *:not(#mainBox), .csdn-side-toolbar, .main_father aside {
        display: none !important;
      }
      .main_father > #mainBox {
        width: unset
      }
      .main_father > #mainBox > main > *:not(.blog-content-box){
        display: none !important;
      }
    }
  </style> 
     </div> 
     <div class="directory-boxshadow-dialog" style="display:none;"> 
      <div class="directory-boxshadow-dialog-box"> 
      </div> 
      <div class="vip-limited-time-offer-box-new" id="vip-limited-time-offer-box-new"> 
       <img class="limited-img limited-img-new" src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fcsdnimg.cn%2Frelease%2Fblogv2%2Fdist%2Fpc%2Fimg%2Fvip-limited-close-newWhite.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461"> 
       <div class="vip-limited-time-top">
         确定要放弃本次机会？ 
       </div> 
       <span class="vip-limited-time-text">福利倒计时</span> 
       <div class="limited-time-box-new"> 
        <span class="time-hour"></span> 
        <i>:</i> 
        <span class="time-minite"></span> 
        <i>:</i> 
        <span class="time-second"></span> 
       </div> 
       <div class="limited-time-vip-box"> 
        <p> <img class="coupon-img" src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fcsdnimg.cn%2Frelease%2Fblogv2%2Fdist%2Fpc%2Fimg%2Fvip-limited-close-roup.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461"> <span class="def">立减 ¥</span> <span class="active limited-num"></span> </p> 
        <span class="">普通VIP年卡可用</span> 
       </div> 
       <a class="limited-time-btn-new" href="https://mall.csdn.net/vip" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.9621&quot;}" data-report-query="spm=1001.2101.3001.9621">立即使用</a> 
      </div> 
     </div>     
     <a id="commentBox" name="commentBox"></a>         
    </main>     
   </div> 
   <div class="recommend-right align-items-stretch clearfix" id="rightAside" data-type="recommend"> 
    <aside class="recommend-right_aside"> 
     <div class="rightside-fixed-hide"> 
     </div> 
     <div id="recommend-right"> 
      <div class="flex-column aside-box groupfile groupfile-active " id="groupfile"> 
       <div class="groupfile-div"> 
        <h3 class="aside-title">目录</h3> 
        <div class="align-items-stretch group_item" id="align-items-stretch-right"> 
         <div class="pos-box"> 
          <div class="scroll-box"> 
           <div class="toc-box"></div> 
          </div> 
         </div> 
        </div> 
        <p class="flexible-btn-new" id="flexible-btn-groupfile" data-report-click="{&quot;spm&quot;:&quot;3001.10782&quot;,&quot;strategy&quot;:&quot;展开全部&quot;}" data-traigger="true" data-minheight="117px" data-maxheight="446px" data-fbox="#align-items-stretch-right"><span class="text">展开全部</span> <img class="look-more" src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fcsdnimg.cn%2Frelease%2Fblogv2%2Fdist%2Fpc%2Fimg%2Farrowup-line-bot-White.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461" alt=""></p> 
        <p class="flexible-btn-new-close close" data-report-click="{&quot;spm&quot;:&quot;3001.10782&quot;,&quot;strategy&quot;:&quot;收起&quot;}" data-traigger="true" data-minheight="117px" data-maxheight="446px" data-fbox="#align-items-stretch-right"><span class="text">收起</span> <img class="look-more" src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fcsdnimg.cn%2Frelease%2Fblogv2%2Fdist%2Fpc%2Fimg%2Farrowup-line-top-White.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461" alt=""></p> 
       </div> 
      </div> 
     </div> 
    </aside> 
   </div> 
   <div class="recommend-right1  align-items-stretch clearfix" id="rightAsideConcision" data-type="recommend"> 
    <aside class="recommend-right_aside"> 
     <div id="recommend-right-concision"> 
      <div class="flex-column aside-box groupfile" id="groupfileConcision"> 
       <div class="groupfile-div1"> 
        <h3 class="aside-title">目录</h3> 
        <div class="align-items-stretch group_item"> 
         <div class="pos-box"> 
          <div class="scroll-box"> 
           <div class="toc-box"></div> 
          </div> 
         </div> 
        </div> 
       </div> 
      </div> 
     </div> 
    </aside> 
   </div> 
  </div> 
  <div class="mask-dark"></div> 
  <div class="skin-boxshadow"></div> 
  <div class="directory-boxshadow"></div> 
  <div class="comment-side-box-shadow comment-side-tit-close" id="commentSideBoxshadow"> 
   <div class="comment-side-content"> 
    <div class="comment-side-tit"> 
     <div class="comment-side-tit-count">
      评论&nbsp;
      <span class="count">10</span>
     </div> 
     <img class="comment-side-tit-close" src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fcsdnimg.cn%2Frelease%2Fblogv2%2Fdist%2Fpc%2Fimg%2FcloseBt.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461">
    </div>  
    <div id="pcFlodCommentSideBox" class="pc-flodcomment-sidebox"> 
     <div class="comment-fold-tit">
      <span id="lookUnFlodComment" class="back"><img src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fcsdnimg.cn%2Frelease%2Fblogv2%2Fdist%2Fpc%2Fimg%2FcommentArrowLeftWhite.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461" alt=""></span>被折叠的&nbsp;
      <span class="count"></span>&nbsp;条评论 
      <a href="https://blogdev.blog.csdn.net/article/details/122245662" class="tip" target="_blank">为什么被折叠?</a> 
      <a href="https://bbs.csdn.net/forums/FreeZone" class="park" target="_blank"> <img src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fcsdnimg.cn%2Frelease%2Fblogv2%2Fdist%2Fpc%2Fimg%2FiconPark.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461">到【灌水乐园】发言</a> 
     </div> 
     <div class="comment-fold-content"></div> 
     <div id="lookBadComment" class="look-bad-comment side-look-comment"> 
      <a class="look-more-comment">查看更多评论<img src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fcsdnimg.cn%2Frelease%2Fblogv2%2Fdist%2Fpc%2Fimg%2FcommentArrowDownWhite.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461" alt=""></a> 
     </div> 
    </div> 
   </div> 
   <div class="comment-rewarddialog-box"> 
    <div class="form-box"> 
     <div class="title-box">
       添加红包 
      <a class="btn-form-close"></a> 
     </div> 
     <form id="commentRewardForm"> 
      <div class="ipt-box"> 
       <label for="txtName">祝福语</label> 
       <div class="ipt-btn-box"> 
        <input type="text" name="name" id="txtName" autocomplete="off" maxlength="50"> 
        <a class="btn-ipt btn-random"></a> 
       </div> 
       <p class="notice">请填写红包祝福语或标题</p> 
      </div> 
      <div class="ipt-box"> 
       <label for="txtSendAmount">红包数量</label> 
       <div class="ipt-txt-box"> 
        <input type="text" name="sendAmount" maxlength="4" id="txtSendAmount" placeholder="请填写红包数量(最小10个)" autocomplete="off"> 
        <span class="after-txt">个</span> 
       </div> 
       <p class="notice">红包个数最小为10个</p> 
      </div> 
      <div class="ipt-box"> 
       <label for="txtMoney">红包总金额</label> 
       <div class="ipt-txt-box error"> 
        <input type="text" name="money" maxlength="5" id="txtMoney" placeholder="请填写总金额(最低5元)" autocomplete="off"> 
        <span class="after-txt">元</span> 
       </div> 
       <p class="notice">红包金额最低5元</p> 
      </div> 
      <div class="balance-info-box"> 
       <label>余额支付</label> 
       <div class="balance-info">
         当前余额
        <span class="balance">3.43</span>元 
        <a href="https://i.csdn.net/#/wallet/balance/recharge" class="link-charge" target="_blank">前往充值 &gt;</a> 
       </div> 
      </div> 
      <div class="opt-box"> 
       <div class="pay-info">
         需支付：
        <span class="price">10.00</span>元 
       </div> 
       <button type="button" class="ml-auto btn-cancel">取消</button> 
       <button type="button" class="ml8 btn-submit" disabled="true">确定</button> 
      </div> 
     </form> 
    </div> 
   </div> 
   <div class="rr-guide-box"> 
    <div class="rr-first-box"> 
     <img src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fcsdnimg.cn%2Frelease%2Fblogv2%2Fdist%2Fpc%2Fimg%2FguideRedReward02.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461" alt=""> 
     <button class="btn-guide-known next">下一步</button> 
    </div> 
    <div class="rr-second-box"> 
     <img src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fcsdnimg.cn%2Frelease%2Fblogv2%2Fdist%2Fpc%2Fimg%2FguideRedReward03.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461" alt=""> 
     <button class="btn-guide-known known">知道了</button> 
    </div> 
   </div> 
  </div> 
  <div class="redEnvolope" id="redEnvolope"> 
   <div class="env-box"> 
    <div class="env-container"> 
     <div class="pre-open" id="preOpen"> 
      <div class="top"> 
       <header> 
        <img class="clearTpaErr" :src="redpacketAuthor.avatar" alt="" src="https://ziquyun.com/main/csdn/img?url=&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461"> 
        <div class="author">
         成就一亿技术人!
        </div> 
       </header> 
       <div class="bot-icon"></div> 
      </div> 
      <footer> 
       <div class="red-openbtn open-start"></div> 
       <div class="tip">
         领取后你会自动成为博主和红包主的粉丝 
        <a class="rule" target="_blank">规则</a> 
       </div> 
      </footer> 
     </div> 
     <div class="opened" id="opened"> 
      <div class="bot-icon"> 
       <header> 
        <a class="creatorUrl" href="" target="_blank"> <img class="clearTpaErr" src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fprofile-avatar.csdnimg.cn%2Fdefault.jpg%212&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461" alt=""> </a> 
        <div class="author"> 
         <div class="tt">
          hope_wisdom
         </div> 发出的红包 
        </div> 
       </header> 
      </div> 
      <div class="receive-box"> 
       <header></header> 
       <div class="receive-list"> 
       </div> 
      </div> 
     </div> 
    </div> 
    <div class="close-btn"></div> 
   </div> 
  </div> 
  <div class="pay-code"> 
   <div class="pay-money">
    实付
    <span class="pay-money-span" data-nowprice="" data-oldprice="">元</span>
   </div> 
   <div class="content-blance">
    <a class="blance-bt" href="javascript:;">使用余额支付</a>
   </div> 
   <div class="content-code"> 
    <div id="payCode" data-id=""> 
     <div class="renovate"> 
      <img src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fcsdnimg.cn%2Frelease%2Fblogv2%2Fdist%2Fpc%2Fimg%2Fpay-time-out.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461"> 
      <span>点击重新获取</span> 
     </div> 
    </div> 
    <div class="pay-style">
     <span><img src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fcsdnimg.cn%2Frelease%2Fblogv2%2Fdist%2Fpc%2Fimg%2Fweixin.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461"></span>
     <span><img src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fcsdnimg.cn%2Frelease%2Fblogv2%2Fdist%2Fpc%2Fimg%2Fzhifubao.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461"></span>
     <span><img src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fcsdnimg.cn%2Frelease%2Fblogv2%2Fdist%2Fpc%2Fimg%2Fjingdong.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461"></span>
     <span class="text">扫码支付</span>
    </div> 
   </div> 
   <div class="bt-close"> 
    <svg t="1567152543821" class="icon" viewbox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10924" xmlns:xlink="http://www.w3.org/1999/xlink" width="12" height="12"> 
     <defs> 
      <style type="text/css"></style> 
     </defs> 
     <path d="M512 438.378667L806.506667 143.893333a52.032 52.032 0 1 1 73.6 73.621334L585.621333 512l294.485334 294.485333a52.074667 52.074667 0 0 1-73.6 73.642667L512 585.621333 217.514667 880.128a52.053333 52.053333 0 1 1-73.621334-73.642667L438.378667 512 143.893333 217.514667a52.053333 52.053333 0 1 1 73.621334-73.621334L512 438.378667z" fill="" p-id="10925"></path> 
    </svg> 
   </div> 
   <div class="pay-balance"> 
    <input type="radio" class="pay-code-radio" data-type="details"> 
    <span class="span">钱包余额</span> 
    <span class="balance" style="color:#FC5531;font-size:14px;">0</span> 
    <div class="pay-code-tile"> 
     <img src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fcsdnimg.cn%2Frelease%2Fblogv2%2Fdist%2Fpc%2Fimg%2Fpay-help.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461" alt=""> 
     <div class="pay-code-content"> 
      <div class="span"> 
       <p class="title">抵扣说明：</p> 
       <p> 1.余额是钱包充值的虚拟货币，按照1:1的比例进行支付金额的抵扣。<br> 2.余额无法直接购买下载，可以购买VIP、付费专栏及课程。</p> 
      </div> 
     </div> 
    </div> 
   </div> 
   <a class="pay-balance-con" href="https://i.csdn.net/#/wallet/balance/recharge" target="_blank"><img src="https://ziquyun.com/main/csdn/img?url=https%3A%2F%2Fcsdnimg.cn%2Frelease%2Fblogv2%2Fdist%2Fpc%2Fimg%2Frecharge.png&amp;rfUrl=https%3A%2F%2Fblog.csdn.net%2Fweixin_52178036%2Farticle%2Fdetails%2F122900461" alt=""><span>余额充值</span></a> 
  </div> 
  <div style="display:none;">  
  </div> 
  <div class="keyword-dec-box" id="keywordDecBox"></div>  
  <!-- 富文本柱状图  --> 
  <link rel="stylesheet" href="https://csdnimg.cn/release/blog_editor_html/release1.6.12/ckeditor/plugins/chart/chart.css">        
  <link rel="stylesheet" href="https://g.csdnimg.cn/lib/cboxEditor/1.1.6/embed-editor.min.css"> 
  <link rel="stylesheet" href="https://csdnimg.cn/release/blog_editor_html/release1.6.12/ckeditor/plugins/codesnippet/lib/highlight/styles/atom-one-dark.css">                  
 </body>
</html>