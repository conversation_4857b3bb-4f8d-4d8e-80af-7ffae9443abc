"""
Python环境配置工具 - Environment Setup Tool

这个工具专门为"计算函数集合"项目设计，用于自动配置和管理Python环境，
确保项目能够在不同的系统上正常运行。

作者: 环境配置助手
版本: 1.0.0
适用于: Python 3.6+
"""

import sys
import os
import subprocess
import platform
import json
import tempfile
import shutil
from pathlib import Path
from typing import List, Dict, Tuple, Optional


class EnvironmentChecker:
    """环境检查器类 - 负责检查系统环境和Python配置"""
    
    def __init__(self):
        """
        初始化环境检查器
        设置基本的配置参数和系统信息
        """
        # 项目所需的最小Python版本
        self.min_python_version = (3, 6)
        
        # 推荐的Python版本
        self.recommended_python_version = (3, 8)
        
        # 项目依赖列表 - 基于README.md和现有代码分析
        self.required_packages = {
            'psutil': '>=5.0.0',        # 性能监控工具
            'numpy': '>=1.19.0',        # 数值计算基础
            'matplotlib': '>=3.0.0',    # 数据可视化
            'scipy': '>=1.5.0'          # 科学计算扩展
        }
        
        # 可选依赖 - 用于增强功能
        self.optional_packages = {
            'jupyter': '>=1.0.0',       # 交互式开发环境
            'pandas': '>=1.1.0',        # 数据分析工具
            'seaborn': '>=0.11.0',      # 高级可视化
            'pytest': '>=6.0.0',       # 测试框架
            'black': '>=21.0.0',        # 代码格式化工具
            'flake8': '>=3.8.0'         # 代码质量检查
        }
        
        # 系统信息收集
        self.system_info = self._get_system_info()
        
        # 当前工作目录
        self.project_root = Path.cwd()
        
        print(f"环境配置工具初始化完成")
        print(f"项目根目录: {self.project_root}")
        print(f"操作系统: {self.system_info['platform']} {self.system_info['version']}")

    def _get_system_info(self) -> Dict[str, str]:
        """
        获取系统基本信息
        用于后续的兼容性检查和配置优化
        
        Returns:
            Dict[str, str]: 包含系统信息的字典
        """
        return {
            'platform': platform.system(),      # 操作系统类型 (Windows/Linux/Darwin)
            'version': platform.version(),      # 系统版本号
            'architecture': platform.machine(), # 系统架构 (x86_64/AMD64等)
            'processor': platform.processor(),  # 处理器信息
            'python_implementation': platform.python_implementation()  # Python实现 (CPython/PyPy等)
        }

    def check_python_version(self) -> Tuple[bool, str]:
        """
        检查Python版本是否满足项目要求
        
        Returns:
            Tuple[bool, str]: (是否满足要求, 详细信息)
        """
        current_version = sys.version_info[:2]  # 获取主版本号和次版本号
        
        # 检查是否满足最低要求
        if current_version < self.min_python_version:
            return False, (
                f"❌ Python版本过低: {current_version[0]}.{current_version[1]}\n"
                f"   最低要求: Python {self.min_python_version[0]}.{self.min_python_version[1]}\n"
                f"   请升级Python版本"
            )
        
        # 检查是否为推荐版本
        if current_version >= self.recommended_python_version:
            return True, (
                f"✅ Python版本: {current_version[0]}.{current_version[1]} (推荐版本)\n"
                f"   完整版本: {sys.version.split()[0]}\n"
                f"   安装路径: {sys.executable}"
            )
        else:
            return True, (
                f"⚠️ Python版本: {current_version[0]}.{current_version[1]} (可用但不是最新推荐版本)\n"
                f"   完整版本: {sys.version.split()[0]}\n"
                f"   推荐升级到: Python {self.recommended_python_version[0]}.{self.recommended_python_version[1]}+\n"
                f"   安装路径: {sys.executable}"
            )

    def check_pip_available(self) -> Tuple[bool, str]:
        """
        检查pip包管理器是否可用
        
        Returns:
            Tuple[bool, str]: (pip是否可用, 详细信息)
        """
        try:
            # 尝试导入pip模块
            import pip
            
            # 获取pip版本信息
            result = subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                pip_version = result.stdout.strip()
                return True, f"✅ pip可用: {pip_version}"
            else:
                return False, f"❌ pip命令执行失败: {result.stderr}"
                
        except ImportError:
            return False, (
                "❌ pip未安装\n"
                "   请访问 https://pip.pypa.io/en/stable/installation/ 安装pip"
            )
        except subprocess.TimeoutExpired:
            return False, "❌ pip命令响应超时"
        except Exception as e:
            return False, f"❌ pip检查失败: {str(e)}"

    def check_virtual_environment(self) -> Tuple[bool, str]:
        """
        检查是否在虚拟环境中运行
        虚拟环境有助于项目依赖隔离
        
        Returns:
            Tuple[bool, str]: (是否在虚拟环境中, 详细信息)
        """
        # 检查各种虚拟环境指示器
        in_venv = (
            hasattr(sys, 'real_prefix') or  # virtualenv
            (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix) or  # venv
            'VIRTUAL_ENV' in os.environ  # 环境变量指示
        )
        
        if in_venv:
            venv_path = os.environ.get('VIRTUAL_ENV', '检测中...')
            return True, (
                f"✅ 运行在虚拟环境中\n"
                f"   虚拟环境路径: {venv_path}\n"
                f"   Python路径: {sys.executable}"
            )
        else:
            return False, (
                "⚠️ 未检测到虚拟环境\n"
                "   建议创建虚拟环境以隔离项目依赖\n"
                "   使用命令: python -m venv project_env"
            )

    def check_packages(self, package_dict: Dict[str, str]) -> Tuple[List[str], List[str]]:
        """
        检查指定包的安装状态
        
        Args:
            package_dict: 包名和版本要求的字典
            
        Returns:
            Tuple[List[str], List[str]]: (已安装的包列表, 缺失的包列表)
        """
        installed = []  # 已安装的包
        missing = []    # 缺失的包
        
        for package_name, version_req in package_dict.items():
            try:
                # 尝试导入包并获取版本信息
                __import__(package_name)
                
                # 使用pip show命令获取详细信息
                result = subprocess.run(
                    [sys.executable, '-m', 'pip', 'show', package_name],
                    capture_output=True, text=True, timeout=5
                )
                
                if result.returncode == 0:
                    # 解析版本信息
                    for line in result.stdout.split('\n'):
                        if line.startswith('Version:'):
                            version = line.split(':', 1)[1].strip()
                            installed.append(f"{package_name} (v{version})")
                            break
                    else:
                        installed.append(f"{package_name} (版本未知)")
                else:
                    missing.append(f"{package_name} {version_req}")
                    
            except ImportError:
                missing.append(f"{package_name} {version_req}")
            except subprocess.TimeoutExpired:
                missing.append(f"{package_name} {version_req} (检查超时)")
            except Exception:
                missing.append(f"{package_name} {version_req} (检查失败)")
        
        return installed, missing

    def comprehensive_check(self) -> Dict[str, any]:
        """
        执行综合环境检查
        检查所有必要的组件和配置
        
        Returns:
            Dict[str, any]: 包含所有检查结果的详细报告
        """
        print("🔍 开始执行环境综合检查...")
        print("=" * 60)
        
        results = {}
        
        # 1. Python版本检查
        print("\n1. 检查Python版本")
        print("-" * 30)
        python_ok, python_info = self.check_python_version()
        results['python'] = {'status': python_ok, 'info': python_info}
        print(python_info)
        
        # 2. pip可用性检查
        print("\n2. 检查pip包管理器")
        print("-" * 30)
        pip_ok, pip_info = self.check_pip_available()
        results['pip'] = {'status': pip_ok, 'info': pip_info}
        print(pip_info)
        
        # 3. 虚拟环境检查
        print("\n3. 检查虚拟环境")
        print("-" * 30)
        venv_ok, venv_info = self.check_virtual_environment()
        results['virtual_env'] = {'status': venv_ok, 'info': venv_info}
        print(venv_info)
        
        # 4. 必需包检查
        print("\n4. 检查必需依赖包")
        print("-" * 30)
        required_installed, required_missing = self.check_packages(self.required_packages)
        results['required_packages'] = {
            'installed': required_installed,
            'missing': required_missing
        }
        
        if required_installed:
            print("✅ 已安装的必需包:")
            for pkg in required_installed:
                print(f"   • {pkg}")
        
        if required_missing:
            print("❌ 缺失的必需包:")
            for pkg in required_missing:
                print(f"   • {pkg}")
        
        # 5. 可选包检查
        print("\n5. 检查可选依赖包")
        print("-" * 30)
        optional_installed, optional_missing = self.check_packages(self.optional_packages)
        results['optional_packages'] = {
            'installed': optional_installed,
            'missing': optional_missing
        }
        
        if optional_installed:
            print("✅ 已安装的可选包:")
            for pkg in optional_installed:
                print(f"   • {pkg}")
        
        if optional_missing:
            print("⚠️ 未安装的可选包:")
            for pkg in optional_missing:
                print(f"   • {pkg}")
        
        # 6. 项目文件检查
        print("\n6. 检查项目文件")
        print("-" * 30)
        project_files_ok, project_files_info = self._check_project_files()
        results['project_files'] = {'status': project_files_ok, 'info': project_files_info}
        print(project_files_info)
        
        return results

    def _check_project_files(self) -> Tuple[bool, str]:
        """
        检查项目核心文件是否存在
        
        Returns:
            Tuple[bool, str]: (文件完整性检查结果, 详细信息)
        """
        # 定义核心项目文件
        core_files = [
            'main.py',
            'non_divisible_finder.py',
            'README.md'
        ]
        
        # 定义可选文件
        optional_files = [
            'test_non_divisible_finder.py',
            'performance_test.py',
            'requirements.txt'
        ]
        
        missing_core = []
        existing_core = []
        missing_optional = []
        existing_optional = []
        
        # 检查核心文件
        for file in core_files:
            if (self.project_root / file).exists():
                existing_core.append(file)
            else:
                missing_core.append(file)
        
        # 检查可选文件
        for file in optional_files:
            if (self.project_root / file).exists():
                existing_optional.append(file)
            else:
                missing_optional.append(file)
        
        # 生成报告
        report_lines = []
        
        if existing_core:
            report_lines.append("✅ 核心项目文件:")
            for file in existing_core:
                report_lines.append(f"   • {file}")
        
        if missing_core:
            report_lines.append("❌ 缺失的核心文件:")
            for file in missing_core:
                report_lines.append(f"   • {file}")
        
        if existing_optional:
            report_lines.append("✅ 现有可选文件:")
            for file in existing_optional:
                report_lines.append(f"   • {file}")
        
        if missing_optional:
            report_lines.append("ℹ️ 可创建的额外文件:")
            for file in missing_optional:
                report_lines.append(f"   • {file}")
        
        # 判断整体状态
        files_ok = len(missing_core) == 0
        
        return files_ok, "\n".join(report_lines)


class EnvironmentSetup:
    """环境设置器类 - 负责自动化的环境配置和包安装"""
    
    def __init__(self, checker: EnvironmentChecker):
        """
        初始化环境设置器
        
        Args:
            checker: 环境检查器实例
        """
        self.checker = checker
        self.project_root = checker.project_root

    def create_requirements_txt(self) -> bool:
        """
        创建requirements.txt文件
        包含所有必需和推荐的依赖包
        
        Returns:
            bool: 创建是否成功
        """
        try:
            requirements_path = self.project_root / 'requirements.txt'
            
            with open(requirements_path, 'w', encoding='utf-8') as f:
                f.write("# 计算函数集合项目 - Python依赖包\n")
                f.write("# 自动生成于环境配置工具\n\n")
                
                f.write("# 必需依赖 - 项目核心功能所需\n")
                for package, version in self.checker.required_packages.items():
                    f.write(f"{package}{version}\n")
                
                f.write("\n# 可选依赖 - 增强功能和开发工具\n")
                for package, version in self.checker.optional_packages.items():
                    f.write(f"# {package}{version}  # 可选: 用于增强功能\n")
            
            print(f"✅ requirements.txt创建成功: {requirements_path}")
            return True
            
        except Exception as e:
            print(f"❌ requirements.txt创建失败: {str(e)}")
            return False

    def install_packages(self, packages: List[str], package_type: str = "包") -> bool:
        """
        安装指定的Python包
        
        Args:
            packages: 要安装的包列表
            package_type: 包类型描述 (用于显示信息)
            
        Returns:
            bool: 安装是否成功
        """
        if not packages:
            print(f"ℹ️ 没有需要安装的{package_type}")
            return True
        
        print(f"\n🔄 开始安装{package_type}...")
        
        # 构建pip install命令
        cmd = [sys.executable, '-m', 'pip', 'install', '--upgrade'] + packages
        
        try:
            print(f"执行命令: {' '.join(cmd)}")
            
            # 执行安装命令
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            
            print(f"✅ {package_type}安装成功!")
            print("安装详情:")
            print(result.stdout)
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ {package_type}安装失败!")
            print(f"错误信息: {e.stderr}")
            return False
        except Exception as e:
            print(f"❌ {package_type}安装过程中发生异常: {str(e)}")
            return False

    def create_virtual_environment(self, venv_name: str = "project_env") -> bool:
        """
        创建Python虚拟环境
        
        Args:
            venv_name: 虚拟环境目录名
            
        Returns:
            bool: 创建是否成功
        """
        venv_path = self.project_root / venv_name
        
        try:
            print(f"🔄 创建虚拟环境: {venv_path}")
            
            # 创建虚拟环境
            subprocess.run([sys.executable, '-m', 'venv', str(venv_path)], check=True)
            
            # 确定激活脚本路径
            if self.checker.system_info['platform'] == 'Windows':
                activate_script = venv_path / 'Scripts' / 'activate.bat'
                pip_executable = venv_path / 'Scripts' / 'pip.exe'
            else:
                activate_script = venv_path / 'bin' / 'activate'
                pip_executable = venv_path / 'bin' / 'pip'
            
            print(f"✅ 虚拟环境创建成功!")
            print(f"   环境路径: {venv_path}")
            print(f"   激活命令: {activate_script}")
            print(f"   使用方法:")
            
            if self.checker.system_info['platform'] == 'Windows':
                print(f"     Windows: {activate_script}")
                print(f"     PowerShell: {venv_path}\\Scripts\\Activate.ps1")
            else:
                print(f"     source {activate_script}")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 虚拟环境创建失败: {e}")
            return False
        except Exception as e:
            print(f"❌ 虚拟环境创建过程中发生异常: {str(e)}")
            return False

    def setup_development_environment(self) -> bool:
        """
        设置完整的开发环境
        包括虚拟环境、依赖安装等
        
        Returns:
            bool: 设置是否成功
        """
        print("\n🚀 开始设置开发环境...")
        print("=" * 50)
        
        success_steps = 0
        total_steps = 4
        
        # 步骤1: 创建requirements.txt
        print(f"\n📝 步骤1/{total_steps}: 创建requirements.txt")
        if self.create_requirements_txt():
            success_steps += 1
        
        # 步骤2: 检查并安装必需依赖
        print(f"\n📦 步骤2/{total_steps}: 安装必需依赖")
        _, missing_required = self.checker.check_packages(self.checker.required_packages)
        if self.install_packages(missing_required, "必需依赖"):
            success_steps += 1
        
        # 步骤3: 询问是否安装可选依赖
        print(f"\n🛠️ 步骤3/{total_steps}: 处理可选依赖")
        _, missing_optional = self.checker.check_packages(self.checker.optional_packages)
        
        if missing_optional:
            response = input(f"是否安装可选依赖包? 这将安装 {len(missing_optional)} 个包 (y/N): ")
            if response.lower() in ['y', 'yes', '是', 'Y']:
                if self.install_packages(missing_optional, "可选依赖"):
                    success_steps += 1
                else:
                    print("⚠️ 可选依赖安装失败，但不影响核心功能")
                    success_steps += 1
            else:
                print("ℹ️ 跳过可选依赖安装")
                success_steps += 1
        else:
            print("✅ 所有可选依赖已安装")
            success_steps += 1
        
        # 步骤4: 验证安装结果
        print(f"\n✅ 步骤4/{total_steps}: 验证安装结果")
        print("重新检查环境状态...")
        
        # 重新检查包状态
        req_installed, req_missing = self.checker.check_packages(self.checker.required_packages)
        
        if not req_missing:
            print("✅ 所有必需依赖已正确安装")
            success_steps += 1
        else:
            print(f"⚠️ 仍有 {len(req_missing)} 个必需依赖未安装")
            for pkg in req_missing:
                print(f"   • {pkg}")
        
        # 生成最终报告
        print(f"\n📊 环境设置完成")
        print("=" * 50)
        print(f"成功步骤: {success_steps}/{total_steps}")
        
        if success_steps == total_steps:
            print("🎉 开发环境设置完成! 项目可以正常运行")
            return True
        else:
            print("⚠️ 部分步骤未完成，可能需要手动处理")
            return False


class ConfigurationManager:
    """配置管理器类 - 负责保存和加载环境配置"""
    
    def __init__(self, project_root: Path):
        """
        初始化配置管理器
        
        Args:
            project_root: 项目根目录路径
        """
        self.project_root = project_root
        self.config_file = project_root / '.env_config.json'

    def save_configuration(self, config_data: Dict) -> bool:
        """
        保存环境配置到文件
        
        Args:
            config_data: 配置数据字典
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 添加时间戳和版本信息
            import datetime
            config_data.update({
                'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'tool_version': '1.0.0',
                'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
            })
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 配置已保存到: {self.config_file}")
            return True
            
        except Exception as e:
            print(f"❌ 配置保存失败: {str(e)}")
            return False

    def load_configuration(self) -> Optional[Dict]:
        """
        从文件加载环境配置
        
        Returns:
            Optional[Dict]: 配置数据字典，加载失败时返回None
        """
        try:
            if not self.config_file.exists():
                return None
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            print(f"✅ 配置已从文件加载: {self.config_file}")
            return config_data
            
        except Exception as e:
            print(f"❌ 配置加载失败: {str(e)}")
            return None


def main():
    """
    主函数 - 程序入口点
    提供交互式界面让用户选择不同的操作
    """
    print("🔧 Python环境配置工具")
    print("=" * 60)
    print("专为 '计算函数集合' 项目设计")
    print("确保您的Python环境能够正常运行项目")
    print("=" * 60)
    
    # 初始化组件
    try:
        checker = EnvironmentChecker()
        setup = EnvironmentSetup(checker)
        config_manager = ConfigurationManager(checker.project_root)
    except Exception as e:
        print(f"❌ 初始化失败: {str(e)}")
        return
    
    while True:
        print("\n🎯 请选择操作:")
        print("1. 执行环境检查")
        print("2. 设置开发环境 (推荐)")
        print("3. 安装缺失依赖")
        print("4. 创建虚拟环境")
        print("5. 生成requirements.txt")
        print("6. 显示系统信息")
        print("7. 保存配置")
        print("8. 加载配置")
        print("0. 退出程序")
        
        try:
            choice = input("\n请输入选项编号 (0-8): ").strip()
            
            if choice == '0':
                print("\n👋 感谢使用Python环境配置工具!")
                break
            
            elif choice == '1':
                print("\n" + "="*60)
                results = checker.comprehensive_check()
                config_manager.save_configuration(results)
            
            elif choice == '2':
                print("\n" + "="*60)
                setup.setup_development_environment()
            
            elif choice == '3':
                print("\n" + "="*60)
                _, missing_required = checker.check_packages(checker.required_packages)
                _, missing_optional = checker.check_packages(checker.optional_packages)
                
                all_missing = missing_required + missing_optional
                if all_missing:
                    setup.install_packages(all_missing, "缺失的依赖")
                else:
                    print("✅ 没有缺失的依赖包")
            
            elif choice == '4':
                print("\n" + "="*60)
                venv_name = input("请输入虚拟环境名称 (默认: project_env): ").strip()
                if not venv_name:
                    venv_name = "project_env"
                setup.create_virtual_environment(venv_name)
            
            elif choice == '5':
                print("\n" + "="*60)
                setup.create_requirements_txt()
            
            elif choice == '6':
                print("\n" + "="*60)
                print("📋 系统信息详情")
                print("-" * 30)
                for key, value in checker.system_info.items():
                    print(f"{key}: {value}")
                
                print(f"\nPython执行路径: {sys.executable}")
                print(f"项目根目录: {checker.project_root}")
            
            elif choice == '7':
                print("\n" + "="*60)
                results = checker.comprehensive_check()
                config_manager.save_configuration(results)
            
            elif choice == '8':
                print("\n" + "="*60)
                config = config_manager.load_configuration()
                if config:
                    print("📄 已保存的配置:")
                    for key, value in config.items():
                        if isinstance(value, dict):
                            print(f"{key}:")
                            for sub_key, sub_value in value.items():
                                print(f"  {sub_key}: {sub_value}")
                        else:
                            print(f"{key}: {value}")
                else:
                    print("⚠️ 没有找到保存的配置文件")
            
            else:
                print("❌ 无效选项，请输入0-8之间的数字")
        
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，再见!")
            break
        except Exception as e:
            print(f"\n❌ 操作过程中发生错误: {str(e)}")
            print("请尝试其他选项或重新启动程序")


if __name__ == '__main__':
    main()
